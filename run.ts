// Playground for learning Vercel AI SDK
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { generateText } from 'ai';

async function testGemini() {
  // Create the Google provider
  const google = createGoogleGenerativeAI({
    apiKey: process.env.GOOGLE_API_KEY, // You'll need to set this
  });

  // Create a model - gemini-1.5-flash is a good starting model
  const model = google('gemini-1.5-flash');

  try {
    console.log('🤖 Calling Gemini...');
    
    // This is the core Vercel AI SDK function - generateText
    const result = await generateText({
      model: model,
      prompt: 'Explain what a large language model is in one sentence.',
    });

    console.log('✅ Response:', result.text);
    console.log('📊 Usage:', result.usage);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run it
testGemini();
