#!/usr/bin/env node

import { Command } from 'commander';
import { LLMClient } from '../../lib/agentic/llm/index.js';

const program = new Command();

program
  .name('agentic')
  .description('CLI for interacting with LLMs using the agentic library')
  .version('1.0.0');

program
  .command('chat')
  .description('Send a message to the LLM and get a streamed response')
  .argument('<message>', 'Message to send to the LLM')
  .option('-m, --model <model>', 'LLM model to use', 'gpt-4o-mini')
  .option('--no-stream', 'Disable streaming output')
  .action(async (message: string, options) => {
    const llm = new LLMClient({ model: options.model });

    if (options.stream) {
      console.log('🤖 Assistant:');
      await llm.streamResponse({
        message,
        onChunk: (chunk) => {
          process.stdout.write(chunk);
        },
        onComplete: () => {
          console.log('\n');
        },
        onError: (error) => {
          console.error('❌ Error:', error.message);
          process.exit(1);
        },
      });
    } else {
      try {
        console.log('🤖 Assistant:');
        const response = await llm.getResponse(message);
        console.log(response);
      } catch (error) {
        console.error('❌ Error:', (error as Error).message);
        process.exit(1);
      }
    }
  });

program
  .command('test')
  .description('Test the connection to the LLM')
  .action(async () => {
    const llm = new LLMClient();
    
    try {
      console.log('🧪 Testing LLM connection...');
      const response = await llm.getResponse('Hello! Please respond with just "Connection successful"');
      console.log('✅ Test successful:', response);
    } catch (error) {
      console.error('❌ Test failed:', (error as Error).message);
      console.error('💡 Make sure you have set your OPENAI_API_KEY environment variable');
      process.exit(1);
    }
  });

program.parse();
