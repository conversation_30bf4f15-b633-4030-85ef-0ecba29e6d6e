# Agentic

A modern TypeScript library and CLI for building LLM-based applications using the Vercel AI SDK.

## Project Structure

```
├── lib/agentic/           # Core library
│   ├── llm/              # LLM client functionality
│   └── agents/           # Agent implementations (placeholder)
├── apps/cli/             # CLI application
└── dist/                 # Compiled output
```

## Setup

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Set your OpenAI API key:
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   ```

3. Build the project:
   ```bash
   pnpm build
   ```

## Usage

### CLI

Test the connection:
```bash
pnpm cli test
```

Send a message to the LLM:
```bash
pnpm cli chat "Hello, how are you?"
```

Use a different model:
```bash
pnpm cli chat "Explain TypeScript" --model gpt-4o
```

Disable streaming:
```bash
pnpm cli chat "Tell me a joke" --no-stream
```

### Library

```typescript
import { LLMClient } from './lib/agentic/llm/index.js';

const llm = new LLMClient({ model: 'gpt-4o-mini' });

// Streaming response
await llm.streamResponse({
  message: 'Hello!',
  onChunk: (chunk) => console.log(chunk),
  onComplete: (response) => console.log('Done:', response),
});

// Non-streaming response
const response = await llm.getResponse('Hello!');
console.log(response);
```

## Development

- `pnpm build` - Build the project
- `pnpm dev` - Build in watch mode
- `pnpm cli` - Run the CLI

## Technology Stack

- **TypeScript** with ESM modules
- **Vercel AI SDK** for LLM integration
- **Commander.js** for CLI interface
- **pnpm** for package management
- **OpenAI** as the default LLM provider
