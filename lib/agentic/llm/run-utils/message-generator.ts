// Utility for generating long messages with configurable length

export interface MessageGeneratorOptions {
  sessionId: string;
  targetChars: number;
  charsPerToken?: number; // Estimate for token calculation
}

/**
 * Generate a long message targeting a specific character count
 * @param options Configuration for message generation
 * @returns Generated message string
 */
export function generateLongMessage(options: MessageGeneratorOptions): string {
  const { sessionId, targetChars, charsPerToken = 6 } = options;
  
  const baseMessage = `Welcome to our comprehensive AI research discussion session ${sessionId}. `;
  
  const contentBlocks = [
    "Artificial intelligence has revolutionized how we approach complex problems across numerous domains. The field encompasses machine learning, deep learning, neural networks, and sophisticated algorithms that can process vast amounts of data to extract meaningful patterns and insights.",
    
    "Machine learning techniques have evolved from simple linear regression to complex ensemble methods, support vector machines, and decision trees. These approaches enable systems to learn from data without explicit programming, adapting their behavior based on experience and feedback.",
    
    "Deep learning represents a paradigm shift in AI, utilizing multi-layered neural networks to automatically discover hierarchical representations in data. Convolutional neural networks excel at image processing, while recurrent networks handle sequential data effectively.",
    
    "The transformer architecture has become the foundation for modern language models, introducing self-attention mechanisms that allow models to focus on relevant parts of input sequences. This breakthrough enabled the development of large language models with unprecedented capabilities.",
    
    "Natural language processing combines computational linguistics with machine learning to enable computers to understand, interpret, and generate human language. Applications range from machine translation to sentiment analysis and conversational AI systems.",
    
    "Computer vision systems can now recognize objects, understand scenes, and even generate realistic images. These capabilities rely on convolutional architectures, attention mechanisms, and massive datasets for training robust visual representations.",
    
    "Reinforcement learning enables agents to learn optimal behaviors through interaction with environments, receiving rewards and penalties for their actions. This approach has achieved remarkable success in game playing, robotics, and autonomous systems.",
    
    "Generative models can create new content by learning the underlying distribution of training data. Variational autoencoders, generative adversarial networks, and diffusion models represent different approaches to this challenging problem.",
    
    "Optimization algorithms are crucial for training AI models effectively. Gradient descent variants, adaptive learning rates, and advanced optimizers help navigate complex loss landscapes to find optimal model parameters.",
    
    "The ethical implications of AI systems require careful consideration of bias, fairness, transparency, and accountability. As these technologies become more prevalent, ensuring responsible development and deployment becomes increasingly important.",
    
    "Multi-modal learning combines different types of data such as text, images, audio, and video to create more comprehensive AI systems. This approach enables richer understanding and more versatile applications across various domains.",
    
    "Transfer learning allows models trained on one task to be adapted for related tasks, reducing the need for large amounts of task-specific training data. This technique has accelerated progress in many AI applications.",
    
    "Federated learning enables training AI models across distributed datasets without centralizing the data, preserving privacy while still benefiting from collective learning. This approach is particularly valuable for sensitive applications.",
    
    "Explainable AI focuses on making AI decision-making processes more transparent and interpretable, which is crucial for building trust and ensuring accountability in high-stakes applications like healthcare and finance.",
    
    "Edge computing brings AI processing closer to data sources, reducing latency and bandwidth requirements while enabling real-time applications in resource-constrained environments like mobile devices and IoT systems."
  ];
  
  let message = baseMessage;
  
  // Add content blocks until we approach target character count
  for (const block of contentBlocks) {
    const testMessage = message + "\n\n" + block;
    
    if (testMessage.length > targetChars) {
      break;
    }
    
    message = testMessage;
  }
  
  const estimatedTokens = Math.round(message.length / charsPerToken);
  console.log(`📊 Generated message with ${message.length} characters (~${estimatedTokens} estimated tokens)`);
  
  return message;
}

/**
 * Convenience function to generate a message targeting a specific token count
 * @param sessionId Session identifier
 * @param targetTokens Target number of tokens
 * @param charsPerToken Estimated characters per token (default: 6)
 * @returns Generated message string
 */
export function generateLongMessageByTokens(
  sessionId: string, 
  targetTokens: number, 
  charsPerToken: number = 6
): string {
  return generateLongMessage({
    sessionId,
    targetChars: targetTokens * charsPerToken,
    charsPerToken
  });
}
