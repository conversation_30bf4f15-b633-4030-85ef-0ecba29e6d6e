import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';

export interface LLMConfig {
  apiKey?: string;
  model?: string;
}

export interface StreamOptions {
  message: string;
  onChunk?: (chunk: string) => void;
  onComplete?: (fullResponse: string) => void;
  onError?: (error: Error) => void;
}

/**
 * Simple LLM client using Vercel AI SDK
 */
export class LLMClient {
  private provider;
  private modelId: string;

  constructor(config: LLMConfig = {}) {
    // Default to OpenAI, but can be extended for other providers
    this.provider = openai({
      apiKey: config.apiKey || process.env.OPENAI_API_KEY,
    });
    this.modelId = config.model || 'gpt-4o-mini';
  }

  /**
   * Stream a response from the LLM
   */
  async streamResponse(options: StreamOptions): Promise<void> {
    try {
      const result = await streamText({
        model: this.provider(this.modelId),
        messages: [
          {
            role: 'user',
            content: options.message,
          },
        ],
      });

      let fullResponse = '';

      for await (const textPart of result.textStream) {
        fullResponse += textPart;
        options.onChunk?.(textPart);
      }

      options.onComplete?.(fullResponse);
    } catch (error) {
      options.onError?.(error as Error);
    }
  }

  /**
   * Get a complete response from the LLM (non-streaming)
   */
  async getResponse(message: string): Promise<string> {
    return new Promise((resolve, reject) => {
      let fullResponse = '';

      this.streamResponse({
        message,
        onChunk: (chunk) => {
          fullResponse += chunk;
        },
        onComplete: (response) => {
          resolve(response);
        },
        onError: (error) => {
          reject(error);
        },
      });
    });
  }
}
