// Playground for learning Vercel AI SDK - Multi-step conversation with caching
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { generateText } from 'ai';

async function testGeminiCaching() {
    const api_key = process.env.GEMINI_API_KEY;
    // Create the Google provider
    const google = createGoogleGenerativeAI({ apiKey: api_key });

    // Use Gemini 2.0 Flash Experimental (latest model)
    const model = google('gemini-2.0-flash-exp');

    // Add some randomness to prevent cache hits between different runs
    const sessionId = Math.random().toString(36).substring(7);
    console.log(`🎲 Session ID: ${sessionId}`);

    // Create a very long context that should trigger caching
    const longContext = `
    You are an expert AI researcher working on a comprehensive analysis of large language models.
    Here is detailed background information about the current state of AI research:

    Large Language Models (LLMs) have revolutionized natural language processing since the introduction of the Transformer architecture in 2017. These models, trained on vast amounts of text data, have demonstrated remarkable capabilities in understanding and generating human-like text across a wide variety of tasks.

    The evolution began with models like GPT-1 (117M parameters) and has progressed to massive models like GPT-4, Claude, and Gemini with hundreds of billions of parameters. The scaling laws discovered by researchers suggest that model performance continues to improve with increased model size, training data, and compute resources.

    Key architectural innovations include:
    - Self-attention mechanisms that allow models to focus on relevant parts of the input
    - Positional encodings that help models understand sequence order
    - Layer normalization for training stability
    - Residual connections that enable deeper networks
    - Multi-head attention for capturing different types of relationships

    Training methodologies have also evolved significantly:
    - Pre-training on diverse internet text to learn general language understanding
    - Fine-tuning on specific tasks to adapt the model's behavior
    - Reinforcement Learning from Human Feedback (RLHF) to align models with human preferences
    - Constitutional AI approaches for safer and more helpful responses
    - Chain-of-thought prompting to improve reasoning capabilities

    Current challenges in the field include:
    - Hallucination: models sometimes generate plausible-sounding but incorrect information
    - Bias: models can perpetuate biases present in their training data
    - Interpretability: understanding how these complex models make decisions
    - Computational costs: training and running large models requires significant resources
    - Safety and alignment: ensuring models behave as intended and don't cause harm

    Recent developments include:
    - Mixture of Experts (MoE) architectures for more efficient scaling
    - Retrieval-augmented generation (RAG) for incorporating external knowledge
    - Multi-modal models that can process text, images, and other data types
    - Tool use capabilities allowing models to interact with external systems
    - Code generation and reasoning improvements

    Session context: ${sessionId}
    `;
