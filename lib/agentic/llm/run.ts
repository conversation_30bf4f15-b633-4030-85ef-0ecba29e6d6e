// Playground for learning Vercel AI SDK - Conversation history with caching
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { generateText } from 'ai';

// Function to generate a very long initial message
function generateLongInitialMessage(sessionId: string): string {
    const topics = [
        "artificial intelligence", "machine learning", "deep learning", "neural networks", 
        "transformer architecture", "attention mechanisms", "language models", "computer vision",
        "natural language processing", "reinforcement learning", "supervised learning", 
        "unsupervised learning", "generative models", "discriminative models", "optimization",
        "gradient descent", "backpropagation", "convolutional networks", "recurrent networks",
        "long short-term memory", "gated recurrent units", "batch normalization", "dropout",
        "regularization", "overfitting", "underfitting", "bias-variance tradeoff", "cross-validation",
        "feature engineering", "dimensionality reduction", "principal component analysis",
        "support vector machines", "random forests", "ensemble methods", "boosting algorithms"
    ];

    let longMessage = `Welcome to our comprehensive AI research discussion session ${sessionId}. `;
    
    // Generate multiple paragraphs about each topic to make it very long
    for (let i = 0; i < topics.length; i++) {
        const topic = topics[i];
        longMessage += `\n\nRegarding ${topic}, this field has seen tremendous advancement in recent years. `;
        longMessage += `The theoretical foundations of ${topic} have been extensively studied by researchers worldwide. `;
        longMessage += `Practical applications of ${topic} span across numerous industries including healthcare, finance, technology, and entertainment. `;
        longMessage += `The computational requirements for ${topic} continue to grow as models become more sophisticated. `;
        longMessage += `Ethical considerations surrounding ${topic} are increasingly important as these technologies become more prevalent. `;
        longMessage += `Future research directions in ${topic} include improving efficiency, interpretability, and robustness. `;
        longMessage += `The intersection of ${topic} with other fields creates exciting opportunities for innovation. `;
        longMessage += `Educational resources for ${topic} are becoming more accessible through online platforms and academic institutions. `;
        longMessage += `Industry adoption of ${topic} requires careful consideration of implementation challenges and best practices. `;
        longMessage += `The societal impact of ${topic} extends beyond technical considerations to include economic and social implications. `;
        longMessage += `Research methodologies in ${topic} continue to evolve with new experimental designs and evaluation metrics. `;
        longMessage += `Collaboration between academia and industry in ${topic} has accelerated innovation and practical deployment. `;
        longMessage += `Open source contributions to ${topic} have democratized access to cutting-edge tools and techniques. `;
        longMessage += `The reproducibility crisis in ${topic} has led to better practices in experimental design and reporting. `;
        longMessage += `Interdisciplinary approaches combining ${topic} with other sciences have yielded breakthrough discoveries. `;
    }

    longMessage += `\n\nThis extensive background provides the foundation for our detailed technical discussion about the current state and future directions of artificial intelligence research and development. The complexity of modern AI systems requires deep understanding across multiple domains.`;
    
    return longMessage;
}

async function testGeminiWithHistory() {
    const api_key = process.env.GEMINI_API_KEY;
    const google = createGoogleGenerativeAI({ apiKey: api_key });
    const model = google('gemini-2.0-flash-exp');

    // Add randomness to prevent cache hits between runs
    const sessionId = Math.random().toString(36).substring(7);
    console.log(`🎲 Session ID: ${sessionId}`);

    // Generate a very long initial message
    const longInitialMessage = generateLongInitialMessage(sessionId);
    console.log(`📏 Initial message length: ${longInitialMessage.length} characters`);

    // Create conversation history with the long message
    const messages = [
        {
            role: 'user' as const,
            content: longInitialMessage
        },
        {
            role: 'assistant' as const,
            content: 'Thank you for this comprehensive overview of AI research topics. I understand we\'re having an in-depth technical discussion about the current state and future of artificial intelligence. What specific aspect would you like to explore further?'
        },
        {
            role: 'user' as const,
            content: 'Given all the context I provided, what do you think are the three most promising research directions for the next decade?'
        }
    ];

    try {
        console.log('🤖 Calling Gemini with conversation history...');
        const startTime = Date.now();

        // This is the key learning: using messages instead of prompt
        const result = await generateText({
            model: model,
            messages: messages,  // Using messages array instead of single prompt
        });

        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log(`✅ Response (${duration}ms):`);
        console.log(result.text);
        console.log('\n📊 Usage:', {
            promptTokens: result.usage?.promptTokens,
            completionTokens: result.usage?.completionTokens,
            totalTokens: result.usage?.totalTokens,
        });

    } catch (error) {
        console.error('❌ Error:', error);
    }
}

// Run it
testGeminiWithHistory();
