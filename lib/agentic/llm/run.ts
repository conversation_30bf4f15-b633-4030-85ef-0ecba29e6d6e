// Playground for learning Vercel AI SDK - Conversation history with caching
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { generateText } from 'ai';
import { readFileSync } from 'fs';
import { join } from 'path';

// Function to generate a message targeting ~3k tokens (rough estimate: 4 chars per token)
function generateLongInitialMessage(sessionId: string): string {
    const baseMessage = `Welcome to our comprehensive AI research discussion session ${sessionId}. `;

    const contentBlocks = [
        "Artificial intelligence has revolutionized how we approach complex problems across numerous domains. The field encompasses machine learning, deep learning, neural networks, and sophisticated algorithms that can process vast amounts of data to extract meaningful patterns and insights.",

        "Machine learning techniques have evolved from simple linear regression to complex ensemble methods, support vector machines, and decision trees. These approaches enable systems to learn from data without explicit programming, adapting their behavior based on experience and feedback.",

        "Deep learning represents a paradigm shift in AI, utilizing multi-layered neural networks to automatically discover hierarchical representations in data. Convolutional neural networks excel at image processing, while recurrent networks handle sequential data effectively.",

        "The transformer architecture has become the foundation for modern language models, introducing self-attention mechanisms that allow models to focus on relevant parts of input sequences. This breakthrough enabled the development of large language models with unprecedented capabilities.",

        "Natural language processing combines computational linguistics with machine learning to enable computers to understand, interpret, and generate human language. Applications range from machine translation to sentiment analysis and conversational AI systems.",

        "Computer vision systems can now recognize objects, understand scenes, and even generate realistic images. These capabilities rely on convolutional architectures, attention mechanisms, and massive datasets for training robust visual representations.",

        "Reinforcement learning enables agents to learn optimal behaviors through interaction with environments, receiving rewards and penalties for their actions. This approach has achieved remarkable success in game playing, robotics, and autonomous systems.",

        "Generative models can create new content by learning the underlying distribution of training data. Variational autoencoders, generative adversarial networks, and diffusion models represent different approaches to this challenging problem.",

        "Optimization algorithms are crucial for training AI models effectively. Gradient descent variants, adaptive learning rates, and advanced optimizers help navigate complex loss landscapes to find optimal model parameters.",

        "The ethical implications of AI systems require careful consideration of bias, fairness, transparency, and accountability. As these technologies become more prevalent, ensuring responsible development and deployment becomes increasingly important."
    ];

    let message = baseMessage;
    const targetChars = 3000 * 6; // More conservative estimate: 6 chars per token = ~18k chars for 3k tokens

    // Add content blocks until we approach target character count
    for (const block of contentBlocks) {
        const testMessage = message + "\n\n" + block;

        if (testMessage.length > targetChars) {
            break;
        }

        message = testMessage;
    }

    console.log(`📊 Generated message with ${message.length} characters (~${Math.round(message.length / 6)} estimated tokens)`);
    return message;
}

async function testGeminiWithHistory() {
    const api_key = process.env.GEMINI_API_KEY;
    const google = createGoogleGenerativeAI({ apiKey: api_key });
    const model = google('gemini-2.0-flash-exp');

    // Add randomness to prevent cache hits between runs
    const sessionId = Math.random().toString(36).substring(7);
    console.log(`🎲 Session ID: ${sessionId}`);

    // Generate a message targeting ~3k tokens
    const longInitialMessage = generateLongInitialMessage(sessionId);

    // Create conversation history with the long message
    const messages = [
        {
            role: 'user' as const,
            content: longInitialMessage
        },
        {
            role: 'assistant' as const,
            content: 'Thank you for this comprehensive overview of AI research topics. I understand we\'re having an in-depth technical discussion about the current state and future of artificial intelligence. What specific aspect would you like to explore further?'
        },
        {
            role: 'user' as const,
            content: 'Given all the context I provided, what do you think are the three most promising research directions for the next decade?'
        }
    ];

    try {
        console.log('🤖 Calling Gemini with conversation history...');
        const startTime = Date.now();

        // This is the key learning: using messages instead of prompt
        const result = await generateText({
            model: model,
            messages: messages,  // Using messages array instead of single prompt
        });

        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log(`✅ Response (${duration}ms):`);
        console.log(result.text);
        console.log('\n📊 Usage:', {
            promptTokens: result.usage?.promptTokens,
            completionTokens: result.usage?.completionTokens,
            totalTokens: result.usage?.totalTokens,
        });

    } catch (error) {
        console.error('❌ Error:', error);
    }
}

// Run it
testGeminiWithHistory();
