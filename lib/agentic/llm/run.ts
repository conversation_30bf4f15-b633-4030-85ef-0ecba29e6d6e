// Playground for learning Vercel AI SDK - Conversation history with caching
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { generateText } from 'ai';
import { readFileSync } from 'fs';
import { join } from 'path';
import { generateLongMessageByTokens } from './run-utils/message-generator.js';

// Configuration for message generation
const TARGET_TOKENS = 3000;
const CHARS_PER_TOKEN = 6;

async function testGeminiWithHistory(sessionId: string) {
    const api_key = process.env.GEMINI_API_KEY;
    const google = createGoogleGenerativeAI({ apiKey: api_key });
    const model = google('gemini-2.5-pro');

    // Add randomness to prevent cache hits between runs

    // Read the system prompt from file
    const currentDir = new URL('.', import.meta.url).pathname;
    const systemPromptPath = join(currentDir, '/run-utils/agent-prompt.txt');
    const systemPrompt = readFileSync(systemPromptPath, 'utf-8');
    console.log(`📄 Loaded system prompt: ${systemPrompt.length} characters`);

    // Generate a message targeting the configured token count
    const longInitialMessage = generateLongMessageByTokens(sessionId, TARGET_TOKENS, CHARS_PER_TOKEN);

    // Create conversation history with system prompt and the long message
    const messages = [
        {
            role: 'system' as const,
            content: systemPrompt
        },
        {
            role: 'user' as const,
            content: [ { "text": longInitialMessage
        },
        {
            role: 'assistant' as const,
            content: 'Thank you for this comprehensive overview of AI research topics. I understand we\'re having an in-depth technical discussion about the current state and future of artificial intelligence. What specific aspect would you like to explore further?'
        },
        {
            role: 'user' as const,
            content: 'Given all the context I provided, what do you think are the three most promising research directions for the next decade?'
        }
    ];

    try {
        console.log('🤖 Calling Gemini with conversation history...');
        const startTime = Date.now();

        // Enable caching for the long context
        const result = await generateText({
            model: model,
            messages: messages,
        });

        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log(`✅ Response (${duration}ms):`);
        console.log(result.text.substring(0, 200) + '...');

        // Enhanced usage reporting to show caching effects
        console.log('\n📊 Token Usage:');
        console.log(`  Prompt tokens: ${result.usage?.promptTokens || 0}`);
        console.log(`  Completion tokens: ${result.usage?.completionTokens || 0}`);
        console.log(`  Total tokens: ${result.usage?.totalTokens || 0}`);
        console.log("Result.usage", result.usage);
        console.log('  Cached tokens:', (result.response.body as any).usageMetadata);

        // Check for caching metadata in provider-specific data
        const providerMetadata = (result as any).providerMetadata?.google;
        if (providerMetadata) {
            console.log('\n🔄 Caching Info:');
            if (providerMetadata.cachedContentTokenCount) {
                console.log(`  Cached tokens: ${providerMetadata.cachedContentTokenCount}`);
            }
            if (providerMetadata.candidatesTokenCount) {
                console.log(`  New tokens processed: ${providerMetadata.candidatesTokenCount}`);
            }
        }

        // Performance metrics
        console.log('\n⚡ Performance:');
        console.log(`  Response time: ${duration}ms`);
        console.log(`  Tokens per second: ${Math.round((result.usage?.completionTokens || 0) / (duration / 1000))}`);

        console.log('\n💡 Run this again quickly to see caching effects!');

    } catch (error) {
        console.error('❌ Error:', error);
        console.log('💡 Note: Caching configuration might not be supported in this version');
        console.log('   Try running without caching options if this persists');
    }
}

// Run it
const sessionId = Math.random().toString(36).substring(7);
console.log(`🎲 Session ID: ${sessionId}`);
await testGeminiWithHistory(sessionId);
await testGeminiWithHistory(sessionId);
